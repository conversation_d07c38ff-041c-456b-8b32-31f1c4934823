@echo off
echo Testing ProfileManager functionality...
echo.

REM Set up classpath
set NIAGARA_HOME=C:\Honeywell\OptimizerSupervisor-N4.13.3.48
set CLASSPATH=%NIAGARA_HOME%\lib\baja.jar;%NIAGARA_HOME%\lib\gson-2.10.1.jar;datasync\datasync-wb\build\classes\java\main;datasync\datasync-wb\build\classes\java\moduleTest

echo Classpath: %CLASSPATH%
echo.

REM Try to run the standalone test
echo Running ProfileManager standalone test...
java -cp "%CLASSPATH%" com.mea.datasync.test.ProfileManagerStandaloneTest

echo.
echo Test completed. Check output above for results.
pause
