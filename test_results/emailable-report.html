<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta charset='utf-8'>
<title>TestNG Report</title>
<style type="text/css">
table {margin-bottom:10px;border-collapse:collapse;empty-cells:show}
td,th {border:1px solid #009;padding:.25em .5em}
.result th {vertical-align:bottom}
.param th {padding-left:1em;padding-right:1em}
.param td {padding-left:.5em;padding-right:2em}
.stripe td,.stripe th {background-color: #E6EBF9}
.numi,.numi_attn {text-align:right}
.total td {font-weight:bold}
.passedodd td {background-color: #0A0}
.passedeven td {background-color: #3F3}
.skippedodd td {background-color: #CCC}
.skippedodd td {background-color: #DDD}
.failedodd td,.numi_attn {background-color: #F33}
.failedeven td,.stripe .numi_attn {background-color: #D00}
.stacktrace {white-space:pre;font-family:monospace}
.totop {font-size:85%;text-align:center;border-bottom:2px solid #000}
</style>
</head>
<body>
<table cellspacing="0" cellpadding="0" class="testOverview">
<tr><th>Test</th><th>Methods<br/>Passed</th><th>Scenarios<br/>Passed</th><th># skipped</th><th># failed</th><th>Total<br/>Time</th><th>Included<br/>Groups</th><th>Excluded<br/>Groups</th></tr>
<tr><td style="text-align:left;padding-right:2em"><a href="#t1">Command line test</a></td><td class="numi">1</td><td class="numi">1</td><td class="numi">0</td><td class="numi">0</td><td class="numi">0.5 seconds</td><td class="numi">persistence </td><td class="numi"></td></tr>
</table>
<table cellspacing="0" cellpadding="0" class="methodOverview" id="summary">
<tr><th>Class</th><th>Method</th><th># of<br/>Scenarios</th><th>Start</th><th>Time<br/>(ms)</th></tr>
<tr id="t1"><th colspan="5">Command line test &#8212; passed</th></tr>
<tr class="passedodd"><td>com.mea.datasync.test.BProfileManagerTest</td><td><a href="#m1"><b>testCompletePersistenceCycle</b> (unit, datasync, standalone, persistence) ("Test complete profile persistence cycle")</a></td><td class="numi">1</td><td>1751063815141</td><td class="numi">425</td></tr>
</table>
<h1>Command line test</h1>
<h2 id="m1">com.mea.datasync.test.BProfileManagerTest:testCompletePersistenceCycle</h2>
<p class="totop"><a href="#summary">back to summary</a></p>
</body></html>
