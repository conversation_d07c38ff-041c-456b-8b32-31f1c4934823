<!DOCTYPE html>

<html>
  <head>
  <meta charset='utf-8'>
  <title>TestNG reports</title>

    <link type="text/css" href="testng-reports.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery-1.7.1.min.js"></script>
    <script type="text/javascript" src="testng-reports.js"></script>
    <script type="text/javascript" src="https://www.google.com/jsapi"></script>
    <script type='text/javascript'>
      google.load('visualization', '1', {packages:['table']});
      google.setOnLoadCallback(drawTable);
      var suiteTableInitFunctions = new Array();
      var suiteTableData = new Array();
    </script>
    <!--
      <script type="text/javascript" src="jquery-ui/js/jquery-ui-1.8.16.custom.min.js"></script>
     -->
  </head>

  <body>
    <div class="top-banner-root">
      <span class="top-banner-title-font">Test results</span>
      <br/>
      <span class="top-banner-font-1">1 suite</span>
    </div> <!-- top-banner-root -->
    <div class="navigator-root">
      <div class="navigator-suite-header">
        <span>All suites</span>
        <a href="#" class="collapse-all-link" title="Collapse/expand all the suites">
          <img class="collapse-all-icon" src="collapseall.gif">
          </img> <!-- collapse-all-icon -->
        </a> <!-- collapse-all-link -->
      </div> <!-- navigator-suite-header -->
      <div class="suite">
        <div class="rounded-window">
          <div class="suite-header light-rounded-window-top">
            <a href="#" class="navigator-link" panel-name="suite-datasyncTest_ProfileManagerTest">
              <span class="suite-name border-passed">datasyncTest_ProfileManagerTest</span>
            </a> <!-- navigator-link -->
          </div> <!-- suite-header light-rounded-window-top -->
          <div class="navigator-suite-content">
            <div class="suite-section-title">
              <span>Info</span>
            </div> <!-- suite-section-title -->
            <div class="suite-section-content">
              <ul>
                <li>
                  <a href="#" class="navigator-link " panel-name="test-xml-datasyncTest_ProfileManagerTest">
                    <span>[unset file name]</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" class="navigator-link " panel-name="testlist-datasyncTest_ProfileManagerTest">
                    <span class="test-stats">1 test</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" class="navigator-link " panel-name="group-datasyncTest_ProfileManagerTest">
                    <span>4 groups</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" class="navigator-link " panel-name="times-datasyncTest_ProfileManagerTest">
                    <span>Times</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" class="navigator-link " panel-name="reporter-datasyncTest_ProfileManagerTest">
                    <span>Reporter output</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" class="navigator-link " panel-name="ignored-methods-datasyncTest_ProfileManagerTest">
                    <span>Ignored methods</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" class="navigator-link " panel-name="chronological-datasyncTest_ProfileManagerTest">
                    <span>Chronological view</span>
                  </a> <!-- navigator-link  -->
                </li>
              </ul>
            </div> <!-- suite-section-content -->
            <div class="result-section">
              <div class="suite-section-title">
                <span>Results</span>
              </div> <!-- suite-section-title -->
              <div class="suite-section-content">
                <ul>
                  <li>
                    <span class="method-stats">1 method,   1 passed</span>
                  </li>
                  <li>
                    <span class="method-list-title passed">Passed methods</span>
                    <span class="show-or-hide-methods passed">
                      <a href="#" panel-name="suite-datasyncTest_ProfileManagerTest" class="hide-methods passed suite-datasyncTest_ProfileManagerTest"> (hide)</a> <!-- hide-methods passed suite-datasyncTest_ProfileManagerTest -->
                      <a href="#" panel-name="suite-datasyncTest_ProfileManagerTest" class="show-methods passed suite-datasyncTest_ProfileManagerTest"> (show)</a> <!-- show-methods passed suite-datasyncTest_ProfileManagerTest -->
                    </span>
                    <div class="method-list-content passed suite-datasyncTest_ProfileManagerTest">
                      <span>
                        <img width="3%" src="passed.png"/>
                        <a href="#" class="method navigator-link" panel-name="suite-datasyncTest_ProfileManagerTest" title="com.mea.datasync.test.BProfileManagerTest" hash-for-method="testCompletePersistenceCycle">testCompletePersistenceCycle</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                    </div> <!-- method-list-content passed suite-datasyncTest_ProfileManagerTest -->
                  </li>
                </ul>
              </div> <!-- suite-section-content -->
            </div> <!-- result-section -->
          </div> <!-- navigator-suite-content -->
        </div> <!-- rounded-window -->
      </div> <!-- suite -->
    </div> <!-- navigator-root -->
    <div class="wrapper">
      <div class="main-panel-root">
        <div panel-name="suite-datasyncTest_ProfileManagerTest" class="panel datasyncTest_ProfileManagerTest">
          <div class="suite-datasyncTest_ProfileManagerTest-class-passed">
            <div class="main-panel-header rounded-window-top">
              <img src="passed.png"/>
              <span class="class-name">com.mea.datasync.test.BProfileManagerTest</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="testCompletePersistenceCycle">
                  </a> <!-- testCompletePersistenceCycle -->
                  <span class="method-name">testCompletePersistenceCycle</span>
                  <em>
(Test complete profile persistence cycle)                  </em>
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-datasyncTest_ProfileManagerTest-class-passed -->
        </div> <!-- panel datasyncTest_ProfileManagerTest -->
        <div panel-name="test-xml-datasyncTest_ProfileManagerTest" class="panel">
          <div class="main-panel-header rounded-window-top">
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <pre>
&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;
&lt;!DOCTYPE suite SYSTEM &quot;http://testng.org/testng-1.0.dtd&quot;&gt;
&lt;suite name=&quot;datasyncTest_ProfileManagerTest&quot;&gt;
  &lt;groups&gt;
    &lt;run&gt;
      &lt;include name=&quot;persistence&quot;/&gt;
    &lt;/run&gt;
  &lt;/groups&gt;
  &lt;test thread-count=&quot;5&quot; name=&quot;Command line test&quot;&gt;
    &lt;classes&gt;
      &lt;class name=&quot;com.mea.datasync.test.BProfileManagerTest&quot;/&gt;
    &lt;/classes&gt;
  &lt;/test&gt; &lt;!-- Command line test --&gt;
&lt;/suite&gt; &lt;!-- datasyncTest_ProfileManagerTest --&gt;
            </pre>
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="testlist-datasyncTest_ProfileManagerTest" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Tests for datasyncTest_ProfileManagerTest</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <ul>
              <li>
                <span class="test-name">Command line test (1 class)</span>
              </li>
            </ul>
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="group-datasyncTest_ProfileManagerTest" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Groups for datasyncTest_ProfileManagerTest</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="test-group">
              <span class="test-group-name">datasync</span>
              <br/>
              <div class="method-in-group">
                <span class="method-in-group-name">testCompletePersistenceCycle</span>
                <br/>
              </div> <!-- method-in-group -->
            </div> <!-- test-group -->
            <div class="test-group">
              <span class="test-group-name">persistence</span>
              <br/>
              <div class="method-in-group">
                <span class="method-in-group-name">testCompletePersistenceCycle</span>
                <br/>
              </div> <!-- method-in-group -->
            </div> <!-- test-group -->
            <div class="test-group">
              <span class="test-group-name">standalone</span>
              <br/>
              <div class="method-in-group">
                <span class="method-in-group-name">testCompletePersistenceCycle</span>
                <br/>
              </div> <!-- method-in-group -->
            </div> <!-- test-group -->
            <div class="test-group">
              <span class="test-group-name">unit</span>
              <br/>
              <div class="method-in-group">
                <span class="method-in-group-name">testCompletePersistenceCycle</span>
                <br/>
              </div> <!-- method-in-group -->
            </div> <!-- test-group -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="times-datasyncTest_ProfileManagerTest" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Times for datasyncTest_ProfileManagerTest</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="times-div">
              <script type="text/javascript">
suiteTableInitFunctions.push('tableData_datasyncTest_ProfileManagerTest');
function tableData_datasyncTest_ProfileManagerTest() {
var data = new google.visualization.DataTable();
data.addColumn('number', 'Number');
data.addColumn('string', 'Method');
data.addColumn('string', 'Class');
data.addColumn('number', 'Time (ms)');
data.addRows(1);
data.setCell(0, 0, 0)
data.setCell(0, 1, 'testCompletePersistenceCycle')
data.setCell(0, 2, 'com.mea.datasync.test.BProfileManagerTest')
data.setCell(0, 3, 425);
window.suiteTableData['datasyncTest_ProfileManagerTest']= { tableData: data, tableDiv: 'times-div-datasyncTest_ProfileManagerTest'}
return data;
}
              </script>
              <span class="suite-total-time">Total running time: 425 ms</span>
              <div id="times-div-datasyncTest_ProfileManagerTest">
              </div> <!-- times-div-datasyncTest_ProfileManagerTest -->
            </div> <!-- times-div -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="reporter-datasyncTest_ProfileManagerTest" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Reporter output for datasyncTest_ProfileManagerTest</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="ignored-methods-datasyncTest_ProfileManagerTest" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">5 ignored methods</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="ignored-class-div">
              <span class="ignored-class-name">com.mea.datasync.test.BProfileManagerTest</span>
              <div class="ignored-methods-div">
                <span class="ignored-method-name">testEnhancedJsonStructure</span>
                <br/>
                <span class="ignored-method-name">testProfileManagerDirectoryOperations</span>
                <br/>
                <span class="ignored-method-name">testProfileManagerSaveLoad</span>
                <br/>
                <span class="ignored-method-name">testProfileManagerBasicOperations</span>
                <br/>
                <span class="ignored-method-name">testProfileValidationAndErrorHandling</span>
                <br/>
              </div> <!-- ignored-methods-div -->
            </div> <!-- ignored-class-div -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="chronological-datasyncTest_ProfileManagerTest" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Methods in chronological order</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="chronological-class">
              <div class="chronological-class-name">com.mea.datasync.test.BProfileManagerTest</div> <!-- chronological-class-name -->
              <div class="configuration-class before">
                <span class="method-name">setupBeforeClass</span>
                <span class="method-start">0 ms</span>
              </div> <!-- configuration-class before -->
              <div class="configuration-method before">
                <span class="method-name">setClassLoader</span>
                <span class="method-start">98 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">testCompletePersistenceCycle</span>
                <span class="method-start">100 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-method after">
                <span class="method-name">restoreClassLoader</span>
                <span class="method-start">527 ms</span>
              </div> <!-- configuration-method after -->
              <div class="configuration-class after">
                <span class="method-name">teardownAfterClass</span>
                <span class="method-start">527 ms</span>
              </div> <!-- configuration-class after -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
      </div> <!-- main-panel-root -->
    </div> <!-- wrapper -->
  </body>
</html>
